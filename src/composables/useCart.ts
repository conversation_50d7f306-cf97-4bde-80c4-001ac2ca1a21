import { CartData } from "../types/cartTypes";
import { Dish } from "../types/commonTypes";
export function useCart() {
  const { $warningToast } = useNuxtApp();
  const { encodedID } = useId();
  const cartDishes = useState<Dish[] | null>("cartDishes", () => null);
  const pending = ref(false);
  const { t } = useI18n();
  const skipNextPusherUpdate = ref(false);
  const isLocalUpdate = ref(false);  // New flag to track local vs remote updates

  const getCart = async () => {
    // Only skip if it's a local update
    if (!encodedID.value || (skipNextPusherUpdate.value && isLocalUpdate.value)) {
      skipNextPusherUpdate.value = false;
      isLocalUpdate.value = false;
      return;
    }
    
    const { data: cartData } = await apiGet<CartData>(
      `cart/${encodedID.value}`
    );
    const dishes = cartData.value?.data?.dish;
    if (dishes) {
      // If we have dishes with complete data already in the cart,
      // merge them with the server response to ensure we have complete data
      if (cartDishes.value && cartDishes.value.length > 0) {
        const mergedDishes = dishes.map(serverDish => {
          const localDish = cartDishes.value?.find(d => d.id === serverDish.id);
          return localDish ? { ...localDish, ...serverDish } : serverDish;
        });
        setCartDishes(mergedDishes);
      } else {
        setCartDishes(dishes);
      }
    }
  };

  const setCartDishes = (dishes: Dish[]) => {
    cartDishes.value = dishes;
  };

  const putDish = async (dishIdsString: string) => {
    isLocalUpdate.value = true;  // Mark this as a local update
    skipNextPusherUpdate.value = true;
    const response = await apiPost<CartData>("/cart", {
      table_id: encodedID.value,
      dishes: dishIdsString,
    });
    return response;
    // if (response?.error?.value) {
    //   $warningToast(t("canNotUpdateCart"));
    //   return response;
    // } else {
    //   return response;
    // }
  };

  const createDishIdsString = (itemModifier: any) => {
    return cartDishes?.value?.map(itemModifier).join(",") || "0-0";
  };

  const updateCartDishes = (itemModifier: any) => {
    if (cartDishes.value) {
      cartDishes.value = cartDishes?.value?.map(itemModifier);
    }
  };

  const addDish = async (id?: number) => {
    pending.value = true;

    // Find the complete dish data from the items list
    const { items } = useTable();
    const dishDetails = items.value?.find(item => item.id === id) || { id, quantity: 1 };

    // Immediately add the dish with complete details to the cart
    cartDishes.value = [...(cartDishes.value || []), { 
      ...dishDetails, 
      quantity: 1 
    }];

    // Prepare the dish IDs string for the API
    const dishes = cartDishes?.value?.map((dish) => [
      `${dish?.id}-${dish?.quantity}`,
    ]);
    let dishIdsString = null;
    if (dishes) {
      dishIdsString = dishes?.join(",") || "0-0";
    } else {
      dishIdsString = `${id}-1` || "0-0";
    }

    isLocalUpdate.value = true;  // Mark this as a local update
    skipNextPusherUpdate.value = true;
    const res = await putDish(dishIdsString as string);

    // Check if the server response is not as expected
    if (res?.error?.value) {
      // Remove the recently added dish from the cart
      cartDishes.value = cartDishes.value.filter((dish) => dish.id !== id);
      setTimeout(() => {
        $warningToast(t("canNotUpdateCart"));
        pending.value = false;
      }, 50);
    } else {
      // Update the cart with the response data, if necessary
      setTimeout(() => {
        const serverDish = res?.data?.value?.data?.dish?.find((d) => d?.id === id);
        if (serverDish) {
          updateCartDishes((d: Dish) => (d.id === id ? serverDish : d));
        }
        pending.value = false;
      }, 50);
    }
  };

  const increaseQuantity = async (id?: number) => {
    pending.value = true;

    // Temporarily update the quantity in the cart
    updateCartDishes((dish: Dish) =>
      dish?.id === id ? { ...dish, quantity: (dish?.quantity || 0) + 1 } : dish
    );

    isLocalUpdate.value = true;  // Mark this as a local update
    skipNextPusherUpdate.value = true;
    const dishIdsString = createDishIdsString((dish: Dish) =>
      `${dish?.id}-${dish?.quantity}`
    );
    
    const res = await putDish(dishIdsString);

    setTimeout(() => {
      if (res?.error?.value) {
        // Revert the quantity update if there's an error
        updateCartDishes((dish: Dish) =>
          dish?.id === id
            ? { ...dish, quantity: (dish?.quantity || 0) - 1 }
            : dish
        );
        $warningToast(t("canNotUpdateCart"));
      }
      pending.value = false;
    }, 50);
  };

  const decreaseQuantity = async (id?: number) => {
    pending.value = true;

    // Store the current state of the cart to revert back in case of an error
    const originalCartState = [...(cartDishes.value || [])];

    // Temporarily decrease the quantity in the cart
    updateCartDishes((dish: Dish) => {
      if (dish?.id === id) {
        return dish?.quantity === 1
          ? null
          : { ...dish, quantity: (dish?.quantity || 0) - 1 };
      }
      return dish;
    });

    // Remove dish if quantity becomes 0
    if (cartDishes.value) {
      cartDishes.value = cartDishes?.value?.filter(Boolean);
    }

    isLocalUpdate.value = true;  // Mark this as a local update
    skipNextPusherUpdate.value = true;
    const dishIdsString = createDishIdsString((dish: Dish) =>
      dish?.id === id && (dish?.quantity || 0) === 1
        ? "0-0"
        : `${dish?.id}-${dish?.quantity}`
    );

    const res = await putDish(dishIdsString);

    setTimeout(() => {
      if (res?.error?.value) {
        // Revert to the original cart state if there's an error
        cartDishes.value = originalCartState;
        $warningToast(t("canNotUpdateCart"));
      }
      pending.value = false;
    }, 50);
  };

  const clearCart = async () => {
    const resu = await putDish("0-0");
    cartDishes.value = [];
  };

  const cartDishesPrice = computed((): number => {
    let result: number = 0;
    if (cartDishes.value) {
      for (let dish of cartDishes.value) {
        result += parseFloat(dish?.price || "0") * (dish?.quantity || 1);
      }
    }
    return result;
  });

  const numberOfDishesInCart = computed((): number => {
    return cartDishes.value?.length || 0;
  });

  return {
    putDish,
    clearCart,
    decreaseQuantity,
    increaseQuantity,
    pending,
    addDish,
    getCart,
    cartDishes,
    cartDishesPrice,
    numberOfDishesInCart,
  };
}
