<script setup>
import { Dropdown, ListGroup, ListGroupItem } from "flowbite-vue";
const { id } = useId();
const { t } = useI18n();
const { capitalizeFirstLetter } = useHelpers();
const { $warningToast, $successToast } = useNuxtApp();
import CallWaiterIcon from "./icons/CallWaiterIcon.vue";
const { numberOfDishesInCart, cartDishes } = useCart();
const { numberOfDishesInOrder, orders } = useOrder();
const config = useRuntimeConfig();
const loading = useLoading();
const user = useAuthUser();
const { logout } = useAuthMethods();

const vibrate = ref(false);
const vibrateOrder = ref(false);
const route = useRoute();
const router = useRouter();
const playAudio = (filename) => {
  const audio = new Audio(config.public.audioBase + filename);
  audio.play();
};

const totalDishQuantity = computed(() => {
  return cartDishes.value
    ? cartDishes.value?.reduce((sum, dish) => sum + dish.quantity, 0)
    : 0;
});
// Watch Cart Addition
watch(totalDishQuantity, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal != 0) {
    playAudio("/cart.mp3"); // play the audio only when the number of dishes changes

    vibrate.value = true;
    setTimeout(() => {
      vibrate.value = false;
    }, 2000);
  } else if (oldVal != 0 && newVal == 0) {
    // vibrateOrder.value = true;
    // playAudio("/checkout.mp3"); // play the audio only when the number of dishes changes
    // setTimeout(() => {
    //   vibrateOrder.value = false;
    // }, 2000);
  }
});

watch(
  () => orders.value?.length,
  (newLength, oldLength) => {
    if (
      oldLength !== undefined &&
      newLength !== undefined &&
      newLength !== null &&
      newLength !== oldLength
    ) {
      vibrateOrder.value = true;
      playAudio("/checkout.mp3"); // play the audio only when the number of dishes changes
      setTimeout(() => {
        vibrateOrder.value = false;
      }, 2000);
    }
  }
);

const waiterCalled = ref(false);
const locale = useLocale();

const getLocale = computed(() => {
  const locales = ["en", "ru"];
  if (locale.value === "en" || locale.value === "ru") {
    return `/${locale.value}`;
  } else {
    return "";
  }
});

const navigate = async (url) => {
  if (route.path !== url) {
    try {
      await navigateTo(url, { 
        pageTransition: {
          name: 'slide',
          mode: 'out-in'
        }
      });
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }
};

const callWaiter = async () => {
  if (!id.value) {
    navigateTo("/login");
  }
  playAudio("/bell.mp3");
  waiterCalled.value = false;
  setTimeout(async () => {
    waiterCalled.value = true;
    const { error } = await apiGet(`/c/${id.value}`);
    if (!error.value) {
      $successToast(t("calledWaiter"));
    } else {
      $warningToast(t("canNotCallwaiter"));
    }
  }, 0);
};

const isAdmin = useState("isAdmin");

const handleLogout = async () => {
  try {
    loading.value = true;
    await logout();
    navigate(`/login`);
  } catch (error) {
    navigate(`/login`);
  }
};
</script>
<template>
  <nav class="p-4 pb-2 bg-white">
    <ul class="flex justify-between">
      <li class="mr-1">
        <nuxt-link
          :to="id ? `/t/${id}` : `/t`"
          class="flex flex-col items-center"
          @click.prevent="navigate(`/t/${id}`)"
          :disabled="loading"
        >
          <Icon name="tabler:home-minus" class="text-cocoa" size="25" />
          <p
            class="mt-2.5 text-xs min-[380px]:text-base text-cocoa font-semibold"
          >
            {{ capitalizeFirstLetter($t("main")) }}
          </p>
        </nuxt-link>
      </li>
      <li class="mr-1 relative">
        <nuxt-link
          :to="id ? `/t/${id}/orders` : '/t'"
          class="flex flex-col items-center"
          @click.prevent="navigate(`/t/${id}/orders`)"
          :disabled="loading"
        >
          <Icon
            name="tabler:list-check"
            class="text-cocoa"
            size="25"
            :class="{ combined: vibrateOrder }"
          />
          <p
            class="mt-2.5 text-xs min-[380px]:text-base text-cocoa font-semibold"
          >
            {{ capitalizeFirstLetter($t("order")) }}
          </p>
          <div
            class="absolute -top-1 right-3 rounded-full bg-velvet text-white shadow w-4 h-4 text-xs flex items-center justify-center"
          >
            {{ numberOfDishesInOrder }}
          </div>
        </nuxt-link>
      </li>
      <li class="min-[312px]:mx-2 -translate-y-1/2">
        <button
          @click="callWaiter"
          class="flex flex-col items-center relative relative outline-none"
          :disabled="loading"
        >
          <CallWaiterIcon
            class="z-10 relative w-[25px] min-[312px]:w-[30px] min-[380px]:w-[100%]"
            :class="{ ball: waiterCalled }"
          />
          <p
            class="mt-1 min-[380px]:mt-2.5 text-[0.7rem] min-[380px]:text-base text-white z-10 relative"
          >
            {{ capitalizeFirstLetter($t("waiter")) }}
          </p>
          <div class="absolute flex justify-center items-center inset-0">
            <div
              class="call-waiter-circle bg-velvet absolute rounded-full"
            ></div>
          </div>
        </button>
      </li>
      <li class="mr-1 relative">
        <nuxt-link
          :to="id ? `/t/${id}/cart` : '/t'"
          class="flex flex-col items-center relative relative outline-none"
          id="cart-button"
          @click.prevent="navigate(`/t/${id}/cart`)"
          :disabled="loading"
        >
          <Icon
            name="tabler:shopping-cart-check"
            class="text-cocoa relative"
            size="25"
            :class="{ combined: vibrate }"
          />
          <p
            class="mt-2.5 text-xs min-[380px]:text-base text-cocoa font-semibold"
          >
            {{ capitalizeFirstLetter($t("basket")) }}
          </p>
          <div
            class="absolute -top-1 right-3 rounded-full bg-velvet text-white shadow w-4 h-4 text-xs flex items-center justify-center"
          >
            {{ totalDishQuantity }}
          </div>
        </nuxt-link>
      </li>
      <li class="mr-1 relative">
        <button
          v-if="user && user.token"
          @click="navigate(`/account`)"
          class="flex flex-col items-center relative relative outline-none"
        >
          <Icon name="tabler:user" class="text-cocoa" size="25" />
          <p
            class="mt-2.5 text-xs min-[380px]:text-base text-cocoa font-semibold"
          >
            {{ capitalizeFirstLetter($t("account")) }}
          </p>
        </button>

        <!-- Button for non-logged in users -->
        <button
          v-else
          class="flex flex-col items-center relative relative outline-none"
          @click="navigate(`/login`)"
          :disabled="loading"
        >
          <Icon name="tabler:user" class="text-cocoa" size="25" />
          <p
            class="mt-2.5 text-xs min-[380px]:text-base text-cocoa font-semibold"
          >
            {{ capitalizeFirstLetter($t("enter")) }}
          </p>
        </button>
      </li>
    </ul>
  </nav>
</template>

<style scoped>
.after-z::after {
  z-index: -1;
}

.call-waiter-circle {
  width: 100px;
  height: 100px;
}

@media (max-width: 380px) {
  .call-waiter-circle {
    width: 72px;
    height: 72px;
  }
}

.ball {
  animation: ring 4s ease-in-out 0.15s;
}

@keyframes ring {
  0% {
    transform: rotate(0);
  }
  1% {
    transform: rotate(30deg);
  }
  3% {
    transform: rotate(-28deg);
  }
  5% {
    transform: rotate(34deg);
  }
  7% {
    transform: rotate(-32deg);
  }
  9% {
    transform: rotate(30deg);
  }
  11% {
    transform: rotate(-28deg);
  }
  13% {
    transform: rotate(26deg);
  }
  15% {
    transform: rotate(-24deg);
  }
  17% {
    transform: rotate(22deg);
  }
  19% {
    transform: rotate(-20deg);
  }
  21% {
    transform: rotate(18deg);
  }
  23% {
    transform: rotate(-16deg);
  }
  25% {
    transform: rotate(14deg);
  }
  27% {
    transform: rotate(-12deg);
  }
  29% {
    transform: rotate(10deg);
  }
  31% {
    transform: rotate(-8deg);
  }
  33% {
    transform: rotate(6deg);
  }
  35% {
    transform: rotate(-4deg);
  }
  37% {
    transform: rotate(2deg);
  }
  39% {
    transform: rotate(-1deg);
  }
  41% {
    transform: rotate(1deg);
  }

  43% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(0);
  }
}

/* Define a new class for the combined animation */
.combined {
  animation: combined-animation 4s ease-in-out;
}

/* Define a new keyframes rule for the combined animation */
@keyframes combined-animation {
  0%,
  100% {
    transform: scale(1) rotate(0);
  }
  10% {
    transform: scale(1.05) rotate(0); /*Scales up to 120% */
  }
  1% {
    transform: scale(1.1) rotate(30deg);
  }
  3% {
    transform: scale(1.15) rotate(-28deg);
  }
  5% {
    transform: scale(1.2) rotate(34deg);
  }
  7% {
    transform: scale(1.25) rotate(-32deg);
  }
  9% {
    transform: scale(1.3) rotate(30deg);
  }
  11% {
    transform: scale(1.35) rotate(-28deg);
  }
  13% {
    transform: scale(1.4) rotate(26deg);
  }
  15% {
    transform: scale(1.45) rotate(-24deg);
  }
  17% {
    transform: scale(1.5) rotate(22deg);
  }
  19% {
    transform: scale(1.55) rotate(-20deg);
  }
  21% {
    transform: scale(1.6) rotate(18deg);
  }
  23% {
    transform: scale(1.75) rotate(-16deg);
  }
  25% {
    transform: scale(1.65) rotate(14deg);
  }
  27% {
    transform: scale(1.55) rotate(-12deg);
  }
  29% {
    transform: scale(1.45) rotate(10deg);
  }
  31% {
    transform: scale(1.4) rotate(-8deg);
  }
  33% {
    transform: scale(1.35) rotate(6deg);
  }
  35% {
    transform: scale(1.3) rotate(-4deg);
  }
  37% {
    transform: scale(1.25) rotate(2deg);
  }
  39% {
    transform: scale(1.2) rotate(-1deg);
  }
  41% {
    transform: scale(1.15) rotate(1deg);
  }
  43% {
    transform: scale(1) rotate(0);
  }
}
</style>
