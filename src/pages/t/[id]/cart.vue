<script setup lang="ts">
import { Alert } from "flowbite-vue";

const { restaurant } = useTable();
const { addOrder, pending: orderPending, orderedDishesPrice, getOrders } = useOrder();
const { cartDishes, cartDishesPrice, clearCart, getCart } = useCart();
const ordered = ref(false);
const { capitalizeFirstLetter } = useHelpers();
const isProcessing = ref(false);

const placeOrderAndClearCart = async () => {
  isProcessing.value = true;
  
  try {
    // Add order
    const data = await addOrder();

    if (data.value) {
      // These operations should complete before we consider the process done
      await getOrders();
      await clearCart();
      ordered.value = true;
    }
  } catch (error) {
    console.error('Error during order process:', error);
  }
  
  // We intentionally don't set isProcessing back to false
  // The button will remain disabled and in processing state
  // until the page is refreshed or navigated away from
};

const feeAmont = computed((): number => {
  if (restaurant.value?.fee) {
    return Number((restaurant.value?.fee / 100) * cartDishesPrice.value);
  } else {
    return 0;
  }
});

const calculatePaymentSummaries = () => {
  const cartTotal = cartDishes.value
    ? cartDishes.value.reduce((sum, dish) => {
        // Check if there is a promo and if newPrice is set in the first promo
        const effectivePrice =
          dish.promos &&
          dish.promos.length > 0 &&
          dish.promos[0] &&
          "newPrice" in dish.promos[0]
            ? dish.promos[0].newPrice
            : dish.price;

        return sum + (effectivePrice as number) * (dish.quantity ?? 0);
      }, 0)
    : 0;

  const orderedTotal =
    orderedDishesPrice.value +
    (restaurant.value?.fee
      ? Number((restaurant.value?.fee / 100) * orderedDishesPrice.value)
      : 0);
  const fee = restaurant.value?.fee
    ? Number((restaurant.value?.fee / 100) * cartTotal)
    : 0;
  const grandTotal = cartTotal + orderedTotal + fee;

  return [
    {
      title: "subTotal",
      price: cartTotal.toFixed(2),
    },
    {
      title: "previouslyOrdered",
      price: orderedTotal.toFixed(2),
    },
    {
      title: "serviceFee",
      price: fee.toFixed(2),
    },
    {
      title: "total",
      price: grandTotal.toFixed(2),
    },
  ];
};

const PaymentSummaries = ref(calculatePaymentSummaries());

watch([cartDishes, orderedDishesPrice, feeAmont], () => {
  PaymentSummaries.value = calculatePaymentSummaries();
});
const loading = useLoading();

onMounted(() => {
  loading.value = false;
});
</script>

<template>
  <NuxtLayout name="table-layout">
    <template #headerTitle>
      {{ capitalizeFirstLetter($t("basket")) }}
    </template>
    <main>
      <section v-if="!ordered">
        <ItemCards
          :items="cartDishes?.map(dish => ({ ...dish, sound: '' })) ?? []"
          :alert="$t('noDishes')"
          :showImage="false"
        />
      </section>
      <section class="mt-50" v-if="cartDishesPrice">
        <ul>
          <PaymentSummary
            v-for="(summary, index) in PaymentSummaries"
            :key="index"
            :title="summary.title"
            :price="summary.price"
          />
        </ul>
        <button
          @click="placeOrderAndClearCart"
          :disabled="isProcessing || orderPending"
          class="bg-velvet w-full mt-4 py-4 font-bold flex justify-center items-center rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <div v-if="isProcessing || orderPending" class="flex items-center justify-center">
            <Icon name="svg-spinners:180-ring" class="mr-2" />
            <span class="font-semibold tracking-widest">{{ $t("processing") }}</span>
          </div>
          <span class="font-semibold tracking-widest" v-else>{{ $t("order") }}</span>
        </button>
      </section>
      <section v-if="ordered">
        <Alert type="success">{{ $t("orderCreatedSuccessfully") }}</Alert>
      </section>
    </main>
  </NuxtLayout>
</template>
