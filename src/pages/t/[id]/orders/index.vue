<script setup lang="ts">
import { Input } from "flowbite-vue";

const { restaurant } = useTable();
const { orders, numberOfDishesInOrder, discount, discountedTotal } = useOrder();
const { checkout } = useCheckout();
import { Alert } from "flowbite-vue";
import { DiscountData } from "~/types/commonTypes";
import { Order } from "~/types/orderTypes";
const { cartDishesPrice } = useCart();
const { id } = useId();
const { t } = useI18n();
const { capitalizeFirstLetter } = useHelpers();
const { $warningToast, $successToast } = useNuxtApp();
const user = useAuthUser();
const { postHistoryData } = useHistory();
const isCheckoutLoading = ref(false);
const isCashCheckoutLoading = ref(false);
const config = useRuntimeConfig();

const discountCode = ref("");
const discountApplied = ref(false);
const discountAmount = ref(0);
const isApplyingDiscount = ref(false);

const tips = ref(0);
const showorderRecievModal = ref(false);
const encodedCheckoutId = ref("");

const activePaymentMethods = computed(() => {
  return (
    restaurant.value?.paymentMethods?.filter((method) => method.active) || []
  );
});

const handlePaymentMethod = async (methodName: string) => {
  if (methodName === "cash") {
    await goToCashCheckout();
  } else if (methodName === "card") {
    await makeCheckout();
  } else {
    $warningToast(t("paymentMethodNotSupported"));
  }
  // Add logic for other payment methods if needed
};

const getMethodButtonClass = (methodName: string) => {
  if (methodName === "cash") {
    return "bg-velvet"; // Existing style for cash
  } else if (methodName === "card") {
    return "bg-velvet"; // Existing style for card
  }
  // Add styles for other methods if needed
  return "bg-gray-500"; // Default style
};

const feeAmountForOrders = computed((): number[] => {
  if (!restaurant.value?.fee) return [];

  return orders.value?.map((order) => {
    const orderPrice = order.dish
      ? order.dish.reduce((sum, dish) => {
          // Check for promo and use newPrice if available
          const effectivePrice =
            dish.promos &&
            dish.promos.length > 0 &&
            dish.promos[0] &&
            "newPrice" in dish.promos[0]
              ? Number(dish.promos[0].newPrice)
              : Number(dish.price);

          return sum + effectivePrice * (dish.quantity ?? 0);
        }, 0)
      : 0;
    const fee =
      restaurant.value?.fee && orderPrice
        ? Number((restaurant.value?.fee / 100) * orderPrice)
        : 0;
    return fee;
  }) as number[];
});

const orderTotalPrices = computed((): number[] => {
  return (orders.value || []).map((order) => {
    const orderPrice = order.dish
      ? order.dish.reduce((sum, dish) => {
          // Check for promo and use newPrice if available
          const effectivePrice =
            dish.promos &&
            dish.promos.length > 0 &&
            dish.promos[0] &&
            "newPrice" in dish.promos[0]
              ? Number(dish.promos[0].newPrice)
              : Number(dish.price);

          return sum + effectivePrice * (dish.quantity ?? 0);
        }, 0)
      : 0;
    return orderPrice;
  }) as number[];
});

const applyDiscount = async () => {
  isApplyingDiscount.value = true;
  if (!discountCode.value) {
    $warningToast(t("enterDiscountCode"));
    isApplyingDiscount.value = false;
    return;
  }
  try {
    // Replace with your actual API request logic
    const response = await apiGet<DiscountData>("/d/" + discountCode.value);

    if (response.data.value?.status && response.data.value.data) {
      discountApplied.value = true;
      discountAmount.value =
        orderTotalPrices.value.reduce(
          (sum, price, index) =>
            sum + price + (feeAmountForOrders.value[index] || 0),
          0
        ) *
        (response.data.value.data.amount / 100);
      discount.value = response.data.value.data;
      $successToast(t("discountApplied"));
    } else {
      discountApplied.value = false;
      $warningToast(t("invalidCode"));
    }
  } catch (error) {
    $warningToast(t("invalidCode"));
    discountApplied.value = false;
  } finally {
    isApplyingDiscount.value = false;
  }
};

const PaymentSummaries = computed(() => {
  const summaries = [];
  orders.value?.forEach((order, index) => {
    if (index != numberOfDishesInOrder.value - 1) {
      summaries.push({
        title: t("order") + ` ${index + 1}`,
        price: (feeAmountForOrders.value[index] && orderTotalPrices.value[index]
          ? feeAmountForOrders.value[index] + orderTotalPrices.value[index]
          : orderTotalPrices.value[index] && !feeAmountForOrders.value[index]
          ? orderTotalPrices.value[index]
          : 0
        ).toFixed(2),
      });
    } else {
      summaries.push(
        {
          title: t("order") + ` ${index + 1}`,
          price: (orderTotalPrices.value[index] || 0).toFixed(2),
        },
        {
          title: t("serviceFee") + ` ${index + 1}`,
          price: (feeAmountForOrders.value[index] || 0).toFixed(2),
        }
      );
    }
  });
  const totalBeforeDiscount = orderTotalPrices.value.reduce(
    (sum, price, index) => sum + price + (feeAmountForOrders.value[index] || 0),
    0
  );

  if (discountApplied.value) {
    summaries.push({
      title: t("discount"),
      price: `-${discountAmount.value.toFixed(2)}`,
    });
  }

  const total = discountApplied.value
    ? totalBeforeDiscount - discountAmount.value + tips.value
    : totalBeforeDiscount + tips.value;
  discountedTotal.value = total;

  summaries.push(
    {
      title: "tip",
      price: tips.value.toFixed(2),
    },
    {
      title: "total",
      price: total.toFixed(2),
    }
  );
  return summaries;
});

const tipses = computed(() => {
  return [
    {
      quantity: 1,
    },
    {
      quantity: 2,
    },
    {
      quantity: 5,
    },
    {
      quantity: 15,
    },
  ];
});

const setTips = (quantity: number): void => {
  if (tips.value === quantity) {
    tips.value = 0;
  } else {
    tips.value = quantity;
  }
};

const goToCashCheckout = async () => {
  try {
    await navigateTo({
      path: `/t/${id.value}/orders/checkout-cash`,
      query: { tips: tips.value },
    });
  } catch (error) {
    console.error("Navigation error:", error);
  }
};

const makeCheckout = async () => {
  isCheckoutLoading.value = true;
  const { error, data, client_ids } = await checkout("card", tips.value);
  
  if (!error.value) {
    showorderRecievModal.value = true;
    discount.value = null;
    discountedTotal.value = 0;
    
    const checkoutId = data?.value?.data?.id;
    encodedCheckoutId.value = btoa(checkoutId || "");
    const checkoutResponseData = data?.value?.data;
    
    if (encodedCheckoutId.value && user.value && user.value?.token && client_ids) {
      const check_url = new URL(
        `/c/${encodedCheckoutId.value}`,
        config.public.siteUrl
      ).toString();
      
      // Create a comma separated string for the ids
      const response = await postHistoryData({
        client_ids: client_ids,
        order_details: new URL(
          `/c/${encodedCheckoutId.value}`,
          config.public.siteUrl
        ).toString(),
        restaurant_id: checkoutResponseData?.restaurant_id,
        amount: checkoutResponseData?.amount,
        quantity: checkoutResponseData?.quantity,
        tip: checkoutResponseData?.tip,
        restaurant_name: restaurant.value?.title,
        restaurant_logo: restaurant.value?.logo,
      });
      
      if (!response.status) {
        $warningToast("Failed to save history data: " + response.message);
      }
    }
  }
  
  // We intentionally don't set isCheckoutLoading back to false
  // The button will remain disabled until the modal is closed or page is navigated away from
};

const goToReceiptPage = async () => {
  if (encodedCheckoutId) {
    await navigateTo({
      path: `/c/${encodedCheckoutId.value}`,
    });
  }
};

const loading = useLoading();

onMounted(() => {
  loading.value = false;
});
</script>

<template>
  <NuxtLayout name="table-layout">
    <template #headerTitle> {{ capitalizeFirstLetter($t("order")) }} </template>
    <main v-if="orders?.length !== 0">
      <section>
        <orderedItems :orders="(orders as Order[])" />
      </section>
      <section class="mt-10">
        <ul>
          <PaymentSummary
            v-for="summary in PaymentSummaries"
            :key="summary.price"
            :title="summary.title"
            :price="summary.price"
          />
        </ul>
      </section>

      <section>
        <h2 class="mt-10">{{ $t("chooseTip") }}</h2>
        <div class="flex gap-4 justify-between mt-2">
          <Button
            v-for="tip in tipses"
            :key="tip.quantity"
            @click="setTips(tip.quantity)"
            class="bg-white w-full border px-5 rounded-lg focus:bg-white hover:bg-white focus:ring-white"
            :class="{ 'border-velvet': tip.quantity === tips }"
          >
            <span class="text-black text-sm">{{ tip.quantity }}</span>
          </Button>
        </div>
      </section>
      <section>
        <div class="mt-4 flex justify-center items-center">
          <Input
            v-model="discountCode"
            :placeholder="t('haveDiscountCode')"
            class="text-center text-xl w-64 caret-red-700 bg-white rounded-xl py-3 text-gray-400 font-medium placeholder-gray-300 focus:border-velvet focus:outline-none focus:ring-velvet"
          />
          <Button
            @click="applyDiscount"
            :disabled="isApplyingDiscount"
            class="h-12 bg-velvet"
          >
            {{ $t("apply") }}
            <div
              v-if="isApplyingDiscount"
              class="w-16 border-none text-center bg-gray-100 flex items-center justify-center"
            >
              <Icon name="svg-spinners:180-ring" />
            </div>
          </Button>
        </div>
      </section>
      <section v-if="activePaymentMethods.length > 0">
        <!-- <Button @click="goToCashCheckout" class="mt-10 py-4 w-full bg-velvet"
          ><span class="font-semibold">{{ $t("payingWithCash") }}</span></Button
        > -->

        <!-- <Button @click="makeCheckout" class="py-4 w-full bg-velvet mt-4">
          <div
            v-if="isCheckoutLoading"
            class="w-8 text-center flex items-center justify-center"
          >
            <Icon name="svg-spinners:180-ring" />
          </div>
          <span v-else class="font-semibold">{{ $t("payingWithCard") }}</span>
        </Button>-->
        <div class="mt-10">
          <Button
            v-for="method in activePaymentMethods"
            :key="method.method_id"
            @click="
              handlePaymentMethod(
                method.paymentMethodData?.method_name as string
              )
            "
            :disabled="
              (method.paymentMethodData?.method_name === 'card' && isCheckoutLoading) ||
              (method.paymentMethodData?.method_name === 'cash' && isCashCheckoutLoading)
            "
            class="py-4 w-full mt-4"
            :class="[
              getMethodButtonClass(method.paymentMethodData?.method_name as string),
              {
                'opacity-50 cursor-not-allowed': 
                  (method.paymentMethodData?.method_name === 'card' && isCheckoutLoading) ||
                  (method.paymentMethodData?.method_name === 'cash' && isCashCheckoutLoading)
              }
            ]"
          >
            <div 
              v-if="
                (method.paymentMethodData?.method_name === 'card' && isCheckoutLoading) ||
                (method.paymentMethodData?.method_name === 'cash' && isCashCheckoutLoading)
              "
              class="flex items-center justify-center"
            >
              <Icon name="svg-spinners:180-ring" class="mr-2" />
              <span class="font-semibold">{{ $t("processing") }}</span>
            </div>
            <span v-else class="font-semibold">{{
              method.paymentMethodData?.method_name.toUpperCase()
            }}</span>
          </Button>
        </div>
      </section>
    </main>
    <Alert v-else type="info">{{ $t("noOrder") }}</Alert>
    <OrderRecievedModal
      :isShowModal="showorderRecievModal"
      @clickOut="showorderRecievModal = false"
      @onReceiptClick="goToReceiptPage"
    />
  </NuxtLayout>
</template>
